<?php

namespace App\Providers;

use App\Services\AccountLocationComparisonService;
use App\Services\DifferenceBetweenTwoCoordinates;
use App\Services\Sales\SalesIncentiveHolder;
use App\Services\UsersBelowFrequencyReportService;
use App\Services\UsersWithoutAmReportService;
use App\Services\UsersWithoutPlansService;
use App\Services\UsersWithoutPmReportService;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Facades\Schema;
class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     *
     * @return void
     */
    public function register()
    {
        try{
            $this->app->singleton(UsersBelowFrequencyReportService::class);
            $this->app->singleton(UsersWithoutAmReportService::class);
            $this->app->singleton(UsersWithoutPlansService::class);
            $this->app->singleton(UsersWithoutPmReportService::class);

            $this->app->singleton(SalesIncentiveHolder::class);

            // Account Location Comparison Services - using bind for Octane compatibility
            $this->app->bind(DifferenceBetweenTwoCoordinates::class);
            $this->app->bind(AccountLocationComparisonService::class, function ($app) {
                return new AccountLocationComparisonService(
                    $app->make(DifferenceBetweenTwoCoordinates::class)
                );
            });

            \Doctrine\DBAL\Types\Type::addType('timestamp', 'App\Services\TimestampType');
        }catch(\Exception $e){
            Log::info($e->getMessage());
        }

    }

    /**
     * Bootstrap any application services.
     *
     * @return void
     */
    public function boot()
    {
        Schema::defaultStringLength(191);
    }
}
