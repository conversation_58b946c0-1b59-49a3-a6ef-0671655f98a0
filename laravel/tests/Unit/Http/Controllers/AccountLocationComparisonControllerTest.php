<?php

namespace Tests\Unit\Http\Controllers;

use App\Http\Controllers\AccountLocationComparisonController;
use App\Services\AccountLocationComparisonService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Validation\ValidationException;
use Mockery;
use Tests\TestCase;

/**
 * Test class for AccountLocationComparisonController
 *
 * Tests the controller functionality including request validation,
 * service integration, and response formatting with proper dependency mocking
 * for Laravel Octane compatibility.
 *
 * @covers \App\Http\Controllers\AccountLocationComparisonController
 */
class AccountLocationComparisonControllerTest extends TestCase
{
    private AccountLocationComparisonController $controller;
    private AccountLocationComparisonService $mockService;

    protected function setUp(): void
    {
        parent::setUp();

        $this->mockService = Mockery::mock(AccountLocationComparisonService::class);
        $this->controller = new AccountLocationComparisonController($this->mockService);
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    /**
     * Test successful report generation
     */
    public function test_generate_report_success(): void
    {
        $requestData = [
            'account_id' => 1,
            'from_date' => '2024-01-01',
            'to_date' => '2024-01-31',
            'distance_threshold' => 1000,
            'recent_visits_limit' => 5
        ];

        $mockReport = [
            'success' => true,
            'summary' => [
                'total_accounts_analyzed' => 1,
                'accounts_with_discrepancies' => 0,
                'total_visits_analyzed' => 3,
                'average_distance_variance' => 250.5,
                'max_distance_variance' => 500.0,
                'accounts_without_registered_location' => 0,
                'accounts_without_visits' => 0
            ],
            'accounts' => [
                [
                    'account_id' => 1,
                    'account_name' => 'Test Account',
                    'account_code' => 'ACC001',
                    'has_discrepancies' => false,
                    'visit_locations' => []
                ]
            ]
        ];

        $this->mockService
            ->shouldReceive('validateReportParameters')
            ->with($requestData)
            ->andReturn(['valid' => true, 'errors' => []]);

        $this->mockService
            ->shouldReceive('generateLocationComparisonReport')
            ->with($requestData)
            ->andReturn($mockReport);

        Log::shouldReceive('info')->andReturnNull();

        // Mock auth
        $this->actingAs($this->createMockUser());

        $request = Request::create('/api/reports/account-location-comparison', 'POST', $requestData);

        $response = $this->controller->generateReport($request);

        $this->assertEquals(200, $response->getStatusCode());
        $responseData = json_decode($response->getContent(), true);
        $this->assertTrue($responseData['success']);
        $this->assertEquals('Location comparison report generated successfully', $responseData['message']);
        $this->assertArrayHasKey('data', $responseData);
    }

    /**
     * Test report generation with validation errors
     */
    public function test_generate_report_validation_error(): void
    {
        $requestData = [
            'account_id' => -1,
            'from_date' => 'invalid-date'
        ];

        $this->mockService
            ->shouldReceive('validateReportParameters')
            ->with($requestData)
            ->andReturn([
                'valid' => false,
                'errors' => [
                    'account_id' => 'Account ID must be a positive integer',
                    'from_date' => 'Invalid from_date format. Use YYYY-MM-DD'
                ]
            ]);

        Log::shouldReceive('info')->andReturnNull();
        Log::shouldReceive('warning')->andReturnNull();

        // Mock auth
        $this->actingAs($this->createMockUser());

        $request = Request::create('/api/reports/account-location-comparison', 'POST', $requestData);

        $response = $this->controller->generateReport($request);

        $this->assertEquals(422, $response->getStatusCode());
        $responseData = json_decode($response->getContent(), true);
        $this->assertFalse($responseData['success']);
        $this->assertEquals('Invalid parameters provided', $responseData['message']);
        $this->assertArrayHasKey('errors', $responseData);
    }

    /**
     * Test report generation with service error
     */
    public function test_generate_report_service_error(): void
    {
        $requestData = [
            'account_id' => 1
        ];

        $mockReport = [
            'success' => false,
            'error' => 'Database connection failed'
        ];

        $this->mockService
            ->shouldReceive('validateReportParameters')
            ->with($requestData)
            ->andReturn(['valid' => true, 'errors' => []]);

        $this->mockService
            ->shouldReceive('generateLocationComparisonReport')
            ->with($requestData)
            ->andReturn($mockReport);

        Log::shouldReceive('info')->andReturnNull();
        Log::shouldReceive('error')->andReturnNull();

        // Mock auth
        $this->actingAs($this->createMockUser());

        $request = Request::create('/api/reports/account-location-comparison', 'POST', $requestData);

        $response = $this->controller->generateReport($request);

        $this->assertEquals(500, $response->getStatusCode());
        $responseData = json_decode($response->getContent(), true);
        $this->assertFalse($responseData['success']);
        $this->assertEquals('Failed to generate report', $responseData['message']);
    }

    /**
     * Test get report info endpoint
     */
    public function test_get_report_info_success(): void
    {
        Log::shouldReceive('error')->never();

        // Mock auth
        $this->actingAs($this->createMockUser());

        $response = $this->controller->getReportInfo();

        $this->assertEquals(200, $response->getStatusCode());
        $responseData = json_decode($response->getContent(), true);
        $this->assertTrue($responseData['success']);
        $this->assertEquals('Report information retrieved successfully', $responseData['message']);
        $this->assertArrayHasKey('data', $responseData);
        $this->assertArrayHasKey('description', $responseData['data']);
        $this->assertArrayHasKey('parameters', $responseData['data']);
        $this->assertArrayHasKey('response_format', $responseData['data']);
    }

    /**
     * Test unexpected exception handling
     */
    public function test_generate_report_unexpected_exception(): void
    {
        $requestData = [
            'account_id' => 1
        ];

        $this->mockService
            ->shouldReceive('validateReportParameters')
            ->andThrow(new \Exception('Unexpected error'));

        Log::shouldReceive('info')->andReturnNull();
        Log::shouldReceive('error')->andReturnNull();

        // Mock auth
        $this->actingAs($this->createMockUser());

        $request = Request::create('/api/reports/account-location-comparison', 'POST', $requestData);

        $response = $this->controller->generateReport($request);

        $this->assertEquals(500, $response->getStatusCode());
        $responseData = json_decode($response->getContent(), true);
        $this->assertFalse($responseData['success']);
        $this->assertEquals('An unexpected error occurred', $responseData['message']);
    }

    /**
     * Test request validation with invalid data types
     */
    public function test_generate_report_invalid_request_format(): void
    {
        $requestData = [
            'account_id' => 'not-a-number',
            'distance_threshold' => 'not-a-number',
            'recent_visits_limit' => 'not-a-number'
        ];

        Log::shouldReceive('info')->andReturnNull();
        Log::shouldReceive('warning')->andReturnNull();

        // Mock auth
        $this->actingAs($this->createMockUser());

        $request = Request::create('/api/reports/account-location-comparison', 'POST', $requestData);

        $response = $this->controller->generateReport($request);

        $this->assertEquals(422, $response->getStatusCode());
        $responseData = json_decode($response->getContent(), true);
        $this->assertFalse($responseData['success']);
        $this->assertEquals('Validation failed', $responseData['message']);
    }

    /**
     * Create a mock user for authentication
     */
    private function createMockUser()
    {
        $user = Mockery::mock();
        $user->shouldReceive('id')->andReturn(1);
        return $user;
    }
}
